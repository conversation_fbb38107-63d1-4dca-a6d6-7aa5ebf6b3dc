#!/usr/bin/env python3
"""
CAMS Data Downloader

This script downloads CAMS dust data from the Copernicus Climate Data Store.
It requires CDS API credentials to be configured.

Author: Solar AI Cleaning & Monitoring Team
License: MIT
"""

import os
import logging
from datetime import datetime, timedelta
import cdsapi

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def download_cams_data(output_file: str = "data_sfc.nc"):
    """
    Download CAMS dust data for the last 7 days.
    
    Args:
        output_file: Output NetCDF file path
    """
    try:
        # Initialize CDS API client
        c = cdsapi.Client()
        
        # Calculate date range (last 7 days)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        # Format dates for API
        date_range = f"{start_date.strftime('%Y-%m-%d')}/{end_date.strftime('%Y-%m-%d')}"
        
        logger.info(f"Downloading CAMS data for date range: {date_range}")
        
        # Download request
        c.retrieve(
            'cams-global-atmospheric-composition-forecasts',
            {
                'variable': [
                    'dust_aerosol_optical_depth_550nm',
                    'dust_aerosol_optical_depth_469nm',
                    'dust_aerosol_optical_depth_670nm',
                    'dust_aerosol_optical_depth_865nm',
                    'dust_aerosol_optical_depth_1240nm',
                ],
                'date': date_range,
                'time': [
                    '00:00', '03:00', '06:00', '09:00',
                    '12:00', '15:00', '18:00', '21:00'
                ],
                'leadtime_hour': [
                    '0', '3', '6', '9', '12', '15', '18', '21',
                    '24', '27', '30', '33', '36', '39', '42', '45',
                    '48', '51', '54', '57', '60', '63', '66', '69', '72'
                ],
                'area': [
                    30, 40, 20, 50,  # North, West, South, East (covers Saudi Arabia)
                ],
                'format': 'netcdf',
            },
            output_file
        )
        
        logger.info(f"CAMS data downloaded successfully to {output_file}")
        return True
        
    except Exception as e:
        logger.error(f"Error downloading CAMS data: {e}")
        return False

if __name__ == "__main__":
    # Create scripts directory if it doesn't exist
    os.makedirs("scripts", exist_ok=True)
    
    # Download data
    success = download_cams_data()
    
    if success:
        logger.info("CAMS data download completed successfully")
    else:
        logger.error("CAMS data download failed")
        exit(1)

