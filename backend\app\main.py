from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
import json
from datetime import datetime

app = FastAPI(title="Solar Panel Monitoring API", version="1.0.0")

# Enable CORS for all origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Data models
class PanelDefect(BaseModel):
    type: str
    severity: float
    detected_at: str

class PanelLocation(BaseModel):
    lat: float
    lng: float

class PanelStatus(BaseModel):
    panel_id: str
    location: PanelLocation
    status: str  # clean, dusty, needs_attention
    dust_level: float  # percentage
    efficiency: float  # percentage
    last_cleaned: str
    defects: List[str]
    battery_level: Optional[float] = None
    temperature: Optional[float] = None

# Mock data for demonstration
mock_panels = [
    PanelStatus(
        panel_id="A23",
        location=PanelLocation(lat=24.7136, lng=46.6753),
        status="clean",
        dust_level=5.0,
        efficiency=95.0,
        last_cleaned="2025-06-18",
        defects=[],
        battery_level=85.0,
        temperature=35.2
    ),
    PanelStatus(
        panel_id="B15",
        location=PanelLocation(lat=24.7140, lng=46.6760),
        status="dusty",
        dust_level=25.0,
        efficiency=75.0,
        last_cleaned="2025-06-10",
        defects=["dust"],
        battery_level=78.0,
        temperature=38.1
    ),
    PanelStatus(
        panel_id="C07",
        location=PanelLocation(lat=24.7145, lng=46.6765),
        status="needs_attention",
        dust_level=40.0,
        efficiency=60.0,
        last_cleaned="2025-06-05",
        defects=["dust", "crack"],
        battery_level=65.0,
        temperature=42.5
    )
]

@app.get("/")
async def root():
    return {"message": "Solar Panel Monitoring API", "version": "1.0.0"}

@app.get("/api/panel-status", response_model=List[PanelStatus])
async def get_panel_status():
    """
    Get the status of all solar panels
    """
    return mock_panels

@app.get("/api/panel-status/{panel_id}", response_model=PanelStatus)
async def get_panel_status_by_id(panel_id: str):
    """
    Get the status of a specific solar panel by ID
    """
    for panel in mock_panels:
        if panel.panel_id == panel_id:
            return panel
    raise HTTPException(status_code=404, detail="Panel not found")

@app.post("/api/panel-status/{panel_id}/update")
async def update_panel_status(panel_id: str, status_update: dict):
    """
    Update the status of a specific solar panel
    """
    for i, panel in enumerate(mock_panels):
        if panel.panel_id == panel_id:
            # Update fields if provided
            if "dust_level" in status_update:
                mock_panels[i].dust_level = status_update["dust_level"]
            if "efficiency" in status_update:
                mock_panels[i].efficiency = status_update["efficiency"]
            if "status" in status_update:
                mock_panels[i].status = status_update["status"]
            if "defects" in status_update:
                mock_panels[i].defects = status_update["defects"]
            if "last_cleaned" in status_update:
                mock_panels[i].last_cleaned = status_update["last_cleaned"]
            
            return {"message": f"Panel {panel_id} updated successfully"}
    
    raise HTTPException(status_code=404, detail="Panel not found")

@app.get("/api/stats")
async def get_system_stats():
    """
    Get overall system statistics
    """
    total_panels = len(mock_panels)
    active_panels = len([p for p in mock_panels if p.status != "offline"])
    panels_needing_cleaning = len([p for p in mock_panels if p.status in ["dusty", "needs_attention"]])
    avg_efficiency = sum(p.efficiency for p in mock_panels) / total_panels if total_panels > 0 else 0
    avg_battery = sum(p.battery_level for p in mock_panels if p.battery_level) / total_panels if total_panels > 0 else 0
    
    return {
        "total_panels": total_panels,
        "active_panels": active_panels,
        "panels_needing_cleaning": panels_needing_cleaning,
        "average_efficiency": round(avg_efficiency, 2),
        "average_battery_level": round(avg_battery, 2),
        "last_updated": datetime.now().isoformat()
    }

@app.post("/api/cleaning/schedule")
async def schedule_cleaning(panel_ids: List[str]):
    """
    Schedule cleaning for specified panels
    """
    scheduled_panels = []
    for panel_id in panel_ids:
        for panel in mock_panels:
            if panel.panel_id == panel_id:
                scheduled_panels.append(panel_id)
                break
    
    if not scheduled_panels:
        raise HTTPException(status_code=404, detail="No valid panels found")
    
    return {
        "message": f"Cleaning scheduled for {len(scheduled_panels)} panels",
        "scheduled_panels": scheduled_panels,
        "scheduled_at": datetime.now().isoformat()
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

