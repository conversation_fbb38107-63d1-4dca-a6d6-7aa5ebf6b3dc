import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { MapPin, Battery, Zap, AlertTriangle, CheckCircle, XCircle } from 'lucide-react'
import './App.css'

function App() {
  const [panels, setPanels] = useState([
    {
      id: 'A23',
      location: { lat: 24.7136, lng: 46.6753 },
      status: 'clean',
      dustLevel: 5,
      efficiency: 95,
      lastCleaned: '2025-06-18',
      defects: []
    },
    {
      id: 'B15',
      location: { lat: 24.7140, lng: 46.6760 },
      status: 'dusty',
      dustLevel: 25,
      efficiency: 75,
      lastCleaned: '2025-06-10',
      defects: ['dust']
    },
    {
      id: 'C07',
      location: { lat: 24.7145, lng: 46.6765 },
      status: 'needs_attention',
      dustLevel: 40,
      efficiency: 60,
      lastCleaned: '2025-06-05',
      defects: ['dust', 'crack']
    }
  ])

  const [language, setLanguage] = useState('en')

  const translations = {
    en: {
      title: 'Solar Panel Monitoring Dashboard',
      subtitle: 'AI-Powered Cleaning & Monitoring System',
      panelStatus: 'Panel Status',
      efficiency: 'Efficiency',
      dustLevel: 'Dust Level',
      lastCleaned: 'Last Cleaned',
      location: 'Location',
      defects: 'Defects',
      clean: 'Clean',
      dusty: 'Dusty',
      needsAttention: 'Needs Attention',
      dust: 'Dust',
      crack: 'Crack',
      hotspot: 'Hot Spot',
      batteryLevel: 'Battery Level',
      totalPanels: 'Total Panels',
      activePanels: 'Active Panels',
      cleaningScheduled: 'Cleaning Scheduled',
      switchLanguage: 'العربية'
    },
    ar: {
      title: 'لوحة مراقبة الألواح الشمسية',
      subtitle: 'نظام التنظيف والمراقبة المدعوم بالذكاء الاصطناعي',
      panelStatus: 'حالة اللوح',
      efficiency: 'الكفاءة',
      dustLevel: 'مستوى الغبار',
      lastCleaned: 'آخر تنظيف',
      location: 'الموقع',
      defects: 'العيوب',
      clean: 'نظيف',
      dusty: 'متسخ',
      needsAttention: 'يحتاج انتباه',
      dust: 'غبار',
      crack: 'شق',
      hotspot: 'نقطة ساخنة',
      batteryLevel: 'مستوى البطارية',
      totalPanels: 'إجمالي الألواح',
      activePanels: 'الألواح النشطة',
      cleaningScheduled: 'تنظيف مجدول',
      switchLanguage: 'English'
    }
  }

  const t = translations[language]

  const getStatusColor = (status) => {
    switch (status) {
      case 'clean': return 'bg-green-500'
      case 'dusty': return 'bg-yellow-500'
      case 'needs_attention': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'clean': return <CheckCircle className="w-4 h-4" />
      case 'dusty': return <AlertTriangle className="w-4 h-4" />
      case 'needs_attention': return <XCircle className="w-4 h-4" />
      default: return null
    }
  }

  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 to-orange-50 p-4 ${language === 'ar' ? 'rtl' : 'ltr'}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">{t.title}</h1>
            <p className="text-lg text-gray-600">{t.subtitle}</p>
          </div>
          <Button 
            onClick={() => setLanguage(language === 'en' ? 'ar' : 'en')}
            variant="outline"
          >
            {t.switchLanguage}
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t.totalPanels}</CardTitle>
              <Zap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">3</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t.activePanels}</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">3</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t.cleaningScheduled}</CardTitle>
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t.batteryLevel}</CardTitle>
              <Battery className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">85%</div>
              <Progress value={85} className="mt-2" />
            </CardContent>
          </Card>
        </div>

        {/* Panel Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {panels.map((panel) => (
            <Card key={panel.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg">Panel {panel.id}</CardTitle>
                  <Badge className={`${getStatusColor(panel.status)} text-white`}>
                    <div className="flex items-center gap-1">
                      {getStatusIcon(panel.status)}
                      {t[panel.status.replace('_', '')]}
                    </div>
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">{t.efficiency}</span>
                  <span className="font-semibold">{panel.efficiency}%</span>
                </div>
                <Progress value={panel.efficiency} className="h-2" />
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">{t.dustLevel}</span>
                  <span className="font-semibold">{panel.dustLevel}%</span>
                </div>
                <Progress value={panel.dustLevel} className="h-2 bg-orange-100" />
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">{t.lastCleaned}</span>
                  <span className="text-sm">{panel.lastCleaned}</span>
                </div>
                
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <MapPin className="w-4 h-4" />
                  <span>{panel.location.lat.toFixed(4)}, {panel.location.lng.toFixed(4)}</span>
                </div>
                
                {panel.defects.length > 0 && (
                  <div>
                    <span className="text-sm text-gray-600 block mb-2">{t.defects}:</span>
                    <div className="flex gap-2 flex-wrap">
                      {panel.defects.map((defect, index) => (
                        <Badge key={index} variant="destructive" className="text-xs">
                          {t[defect]}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}

export default App

