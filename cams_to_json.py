#!/usr/bin/env python3
"""
CAMS Data to JSON Converter

This script processes CAMS (Copernicus Atmosphere Monitoring Service) dust data
and converts it to JSON format for use in the solar panel monitoring system.
It automates the process of extracting dust forecast data from NetCDF files.

Author: Solar AI Cleaning & Monitoring Team
License: MIT

Requirements:
- pip install netCDF4 xarray pandas numpy
"""

import json
import logging
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import numpy as np
import pandas as pd
import xarray as xr

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CAMSDataProcessor:
    def __init__(self, data_file: str = "data_sfc.nc"):
        """
        Initialize CAMS data processor.
        
        Args:
            data_file: Path to the CAMS NetCDF data file
        """
        self.data_file = data_file
        self.dataset = None
        self.riyadh_coords = {"lat": 24.7136, "lon": 46.6753}
        
    def load_data(self) -> bool:
        """
        Load CAMS NetCDF data file.
        
        Returns:
            True if data loaded successfully, False otherwise
        """
        try:
            if not os.path.exists(self.data_file):
                logger.error(f"Data file {self.data_file} not found")
                return False
            
            self.dataset = xr.open_dataset(self.data_file)
            logger.info(f"Successfully loaded CAMS data from {self.data_file}")
            logger.info(f"Dataset dimensions: {dict(self.dataset.dims)}")
            logger.info(f"Dataset variables: {list(self.dataset.data_vars)}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading CAMS data: {e}")
            return False
    
    def get_nearest_grid_point(self, target_lat: float, target_lon: float) -> Tuple[float, float]:
        """
        Find the nearest grid point to the target coordinates.
        
        Args:
            target_lat: Target latitude
            target_lon: Target longitude
            
        Returns:
            Tuple of (nearest_lat, nearest_lon)
        """
        if self.dataset is None:
            raise ValueError("Dataset not loaded. Call load_data() first.")
        
        # Get coordinate arrays
        lats = self.dataset.coords['latitude'].values
        lons = self.dataset.coords['longitude'].values
        
        # Find nearest latitude
        lat_idx = np.argmin(np.abs(lats - target_lat))
        nearest_lat = lats[lat_idx]
        
        # Find nearest longitude
        lon_idx = np.argmin(np.abs(lons - target_lon))
        nearest_lon = lons[lon_idx]
        
        logger.info(f"Target: ({target_lat}, {target_lon})")
        logger.info(f"Nearest grid point: ({nearest_lat}, {nearest_lon})")
        
        return nearest_lat, nearest_lon
    
    def extract_dust_data(self, lat: float, lon: float, 
                         start_time: Optional[datetime] = None,
                         end_time: Optional[datetime] = None) -> Dict:
        """
        Extract dust-related variables for a specific location and time range.
        
        Args:
            lat: Latitude
            lon: Longitude
            start_time: Start time for data extraction (optional)
            end_time: End time for data extraction (optional)
            
        Returns:
            Dictionary containing extracted dust data
        """
        if self.dataset is None:
            raise ValueError("Dataset not loaded. Call load_data() first.")
        
        # Find nearest grid point
        nearest_lat, nearest_lon = self.get_nearest_grid_point(lat, lon)
        
        # Select data at the nearest grid point
        point_data = self.dataset.sel(latitude=nearest_lat, longitude=nearest_lon, method='nearest')
        
        # Apply time filtering if specified
        if start_time or end_time:
            if start_time:
                point_data = point_data.sel(time=slice(start_time, end_time))
        
        # Extract dust-related variables
        dust_data = {}
        
        # Common CAMS dust variables (adjust based on actual dataset variables)
        dust_variables = [
            'duaod550',  # Dust Aerosol Optical Depth at 550nm
            'duaod469',  # Dust Aerosol Optical Depth at 469nm
            'duaod670',  # Dust Aerosol Optical Depth at 670nm
            'duaod865',  # Dust Aerosol Optical Depth at 865nm
            'duaod1240', # Dust Aerosol Optical Depth at 1240nm
            'dusmass',   # Dust mass mixing ratio
            'dusmass25', # Dust mass mixing ratio (0.03-0.55μm)
            'dusmass',   # Total dust mass
        ]
        
        # Extract available variables
        for var in dust_variables:
            if var in point_data.data_vars:
                try:
                    data_array = point_data[var]
                    dust_data[var] = {
                        'values': data_array.values.tolist(),
                        'times': [pd.to_datetime(t).isoformat() for t in data_array.time.values],
                        'units': data_array.attrs.get('units', 'unknown'),
                        'long_name': data_array.attrs.get('long_name', var)
                    }
                    logger.info(f"Extracted {var}: {len(data_array.values)} time points")
                except Exception as e:
                    logger.warning(f"Error extracting {var}: {e}")
        
        # If no specific dust variables found, try to extract any aerosol-related data
        if not dust_data:
            logger.warning("No dust-specific variables found. Extracting all available variables.")
            for var in point_data.data_vars:
                try:
                    data_array = point_data[var]
                    if len(data_array.dims) > 0 and 'time' in data_array.dims:
                        dust_data[var] = {
                            'values': data_array.values.tolist(),
                            'times': [pd.to_datetime(t).isoformat() for t in data_array.time.values],
                            'units': data_array.attrs.get('units', 'unknown'),
                            'long_name': data_array.attrs.get('long_name', var)
                        }
                except Exception as e:
                    logger.warning(f"Error extracting {var}: {e}")
        
        return dust_data
    
    def create_forecast_json(self, lat: float, lon: float, 
                           output_file: str = "dust_riyadh.json") -> bool:
        """
        Create a JSON forecast file for a specific location.
        
        Args:
            lat: Latitude
            lon: Longitude
            output_file: Output JSON file path
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Extract dust data
            dust_data = self.extract_dust_data(lat, lon)
            
            if not dust_data:
                logger.error("No dust data extracted")
                return False
            
            # Create forecast structure
            forecast = {
                "location": {
                    "latitude": lat,
                    "longitude": lon,
                    "name": "Riyadh, Saudi Arabia"
                },
                "generated_at": datetime.now().isoformat(),
                "data_source": "CAMS (Copernicus Atmosphere Monitoring Service)",
                "forecast_data": dust_data,
                "metadata": {
                    "file_source": self.data_file,
                    "processing_script": "cams_to_json.py",
                    "variables_extracted": list(dust_data.keys())
                }
            }
            
            # Add summary statistics if data is available
            if dust_data:
                summary = {}
                for var, data in dust_data.items():
                    if 'values' in data and data['values']:
                        values = [v for v in data['values'] if v is not None and not np.isnan(v)]
                        if values:
                            summary[var] = {
                                "min": float(np.min(values)),
                                "max": float(np.max(values)),
                                "mean": float(np.mean(values)),
                                "std": float(np.std(values))
                            }
                forecast["summary_statistics"] = summary
            
            # Write to JSON file
            with open(output_file, 'w') as f:
                json.dump(forecast, f, indent=2, default=str)
            
            logger.info(f"Forecast JSON saved to {output_file}")
            logger.info(f"Variables included: {list(dust_data.keys())}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error creating forecast JSON: {e}")
            return False
    
    def get_current_dust_level(self, lat: float, lon: float) -> Optional[float]:
        """
        Get current dust level for a specific location.
        
        Args:
            lat: Latitude
            lon: Longitude
            
        Returns:
            Current dust level (AOD) or None if not available
        """
        try:
            dust_data = self.extract_dust_data(lat, lon)
            
            # Try to get the most recent dust AOD value
            for var in ['duaod550', 'duaod469', 'duaod670']:
                if var in dust_data and dust_data[var]['values']:
                    values = dust_data[var]['values']
                    if values and len(values) > 0:
                        # Return the last (most recent) value
                        return float(values[-1]) if not np.isnan(values[-1]) else None
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting current dust level: {e}")
            return None
    
    def close(self):
        """Close the dataset."""
        if self.dataset is not None:
            self.dataset.close()

def main():
    """Main function to process CAMS data and create JSON output."""
    # Initialize processor
    processor = CAMSDataProcessor("data_sfc.nc")
    
    try:
        # Load data
        if not processor.load_data():
            logger.error("Failed to load CAMS data")
            return 1
        
        # Process data for Riyadh
        riyadh_lat = 24.7136
        riyadh_lon = 46.6753
        
        logger.info(f"Processing CAMS data for Riyadh ({riyadh_lat}, {riyadh_lon})")
        
        # Create forecast JSON
        success = processor.create_forecast_json(
            riyadh_lat, 
            riyadh_lon, 
            "dust_riyadh.json"
        )
        
        if success:
            logger.info("Successfully created dust forecast JSON")
            
            # Get current dust level
            current_dust = processor.get_current_dust_level(riyadh_lat, riyadh_lon)
            if current_dust is not None:
                logger.info(f"Current dust level (AOD): {current_dust:.4f}")
            
            return 0
        else:
            logger.error("Failed to create forecast JSON")
            return 1
            
    except Exception as e:
        logger.error(f"Error in main: {e}")
        return 1
    
    finally:
        processor.close()

if __name__ == "__main__":
    sys.exit(main())

