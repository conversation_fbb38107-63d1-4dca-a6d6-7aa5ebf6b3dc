# dust_forecast.py

This script consumes the iDust API to retrieve dust forecast data for a specified location and returns it as a JSON object for the next 72 hours.

## Prerequisites

- Python 3.x
- `requests` library (`pip install requests`)

## Usage

1. Replace `YOUR_API_KEY` with your actual iDust API key.
2. Replace `YOUR_LATITUDE` and `YOUR_LONGITUDE` with the desired coordinates.
3. Run the script:

```bash
python dust_forecast.py
```

## Code

```python
import requests
import json
from datetime import datetime, timedelta

# Configuration
API_KEY = "YOUR_API_KEY"  # Replace with your iDust API key
BASE_URL = "https://api.idust.com/v1/forecast"  # Example API endpoint, verify with actual iDust API docs
LATITUDE = 24.7136  # Example: Riyadh latitude
LONGITUDE = 46.6753 # Example: Riyadh longitude

def get_dust_forecast(api_key, latitude, longitude):
    """
    Fetches dust forecast data from the iDust API for the next 72 hours.
    """
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # iDust API might require specific parameters for time range or number of hours
    # This is a placeholder, adjust based on actual API documentation
    params = {
        "lat": latitude,
        "lon": longitude,
        "hours": 72  # Requesting forecast for the next 72 hours
    }

    try:
        response = requests.get(BASE_URL, headers=headers, params=params)
        response.raise_for_status()  # Raise an exception for HTTP errors
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error fetching dust forecast: {e}")
        return None

if __name__ == "__main__":
    forecast_data = get_dust_forecast(API_KEY, LATITUDE, LONGITUDE)
    if forecast_data:
        print(json.dumps(forecast_data, indent=4))
    else:
        print("Failed to retrieve dust forecast data.")

```


