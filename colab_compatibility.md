# Google Colab Compatibility Guide

This document outlines how to run the Solar AI Cleaning & Monitoring System components in Google Colab, ensuring compatibility with cloud-based development and testing environments.

## 1. Setting Up the Environment

### 1.1 Mount Google Drive

```python
from google.colab import drive
drive.mount('/content/drive')
```

### 1.2 Clone or Upload Project

If your project is on GitHub:

```python
!git clone https://github.com/your-username/solar-ai-monitoring.git
%cd solar-ai-monitoring
```

Or if you've uploaded to Google Drive:

```python
%cd /content/drive/MyDrive/solar-ai-monitoring
```

### 1.3 Install Dependencies

```python
!pip install ultralytics fastapi uvicorn twilio requests pandas numpy matplotlib seaborn xarray netCDF4
!pip install torch torchvision
```

## 2. Running YOLOv8 Training

### 2.1 Prepare Dataset

```python
# Create dataset directory structure if needed
!mkdir -p datasets/solar_panels/images/train
!mkdir -p datasets/solar_panels/images/val
!mkdir -p datasets/solar_panels/labels/train
!mkdir -p datasets/solar_panels/labels/val

# If dataset is in Google Drive, copy it to the working directory
!cp -r /content/drive/MyDrive/solar_datasets/solar_panels/* datasets/solar_panels/
```

### 2.2 Run Training Notebook

```python
# Run the training notebook
%run train_yolov8.ipynb
```

Or open and run the notebook directly:

```python
from IPython.display import display, HTML
display(HTML('<a href="./train_yolov8.ipynb" target="_blank">Open Training Notebook</a>'))
```

## 3. Processing Dust Data

### 3.1 Download Sample Data

```python
# Download sample dust data if needed
!wget -O dust_riyadh.nc https://your-data-source.com/sample_dust_data.nc
```

### 3.2 Run Data Processing

```python
%run cams_to_json.py
```

### 3.3 Visualize Results

```python
import json
import matplotlib.pyplot as plt
import pandas as pd

# Load processed data
with open('dust_riyadh.json', 'r') as f:
    dust_data = json.load(f)

# Extract and plot data
if 'forecast_data' in dust_data and 'duaod550' in dust_data['forecast_data']:
    values = dust_data['forecast_data']['duaod550']['values']
    times = dust_data['forecast_data']['duaod550']['times']
    
    plt.figure(figsize=(12, 6))
    plt.plot(times, values)
    plt.title('Dust Aerosol Optical Depth at 550nm')
    plt.xlabel('Time')
    plt.ylabel('AOD')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()
```

## 4. Running FastAPI Server in Colab

### 4.1 Start Server in Background

```python
import subprocess
import time
import requests

# Start FastAPI server in background
server_process = subprocess.Popen(['python', 'fastapi_app.py'])
time.sleep(5)  # Wait for server to start
```

### 4.2 Test API Endpoints

```python
# Test API endpoints
try:
    response = requests.get('http://localhost:8000/api/panel-status')
    print("API Response:", response.json())
except Exception as e:
    print("Error accessing API:", e)
```

### 4.3 Create Tunnel for External Access (Optional)

```python
# Install pyngrok if needed
!pip install pyngrok

from pyngrok import ngrok

# Start ngrok tunnel
public_url = ngrok.connect(8000)
print(f"Public URL: {public_url}")
```

## 5. Running Dashboard in Colab

### 5.1 Install Node.js

```python
# Install Node.js
!curl -sL https://deb.nodesource.com/setup_16.x | bash -
!apt-get install -y nodejs
```

### 5.2 Install Dashboard Dependencies

```python
%cd solar-dashboard
!npm install
```

### 5.3 Build Dashboard

```python
!npm run build
```

### 5.4 Serve Dashboard

```python
# Install serve if needed
!npm install -g serve

# Serve the built dashboard
dashboard_process = subprocess.Popen(['serve', '-s', 'dist'])
```

## 6. Running Tests in Colab

### 6.1 Run API Tests

```python
!python -m unittest tests/test_api.py
```

### 6.2 Run Dust Forecast Tests

```python
!python -m unittest tests/test_dust_forecast.py
```

## 7. Raspberry Pi Compatibility

The system is designed to be compatible with Raspberry Pi 4 (4GB+ RAM) running Raspberry Pi OS. Key considerations:

### 7.1 Hardware Requirements

- Raspberry Pi 4 with 4GB+ RAM
- 32GB+ microSD card
- Optional: FLIR thermal camera
- Optional: Sony IMX477 camera
- Optional: MPU6050 sensor via I2C

### 7.2 Software Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python dependencies
sudo apt install -y python3-pip python3-venv

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install Python packages
pip install -r requirements.txt

# Install Node.js for dashboard
curl -sL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt install -y nodejs

# Set up dashboard
cd solar-dashboard
npm install
npm run build
```

## 8. Troubleshooting

### 8.1 Common Issues in Colab

- **GPU Memory Issues**: If you encounter GPU memory errors during YOLOv8 training, try:
  ```python
  # Switch to CPU
  %env CUDA_VISIBLE_DEVICES=-1
  ```

- **Timeout Issues**: For long-running processes, keep Colab active:
  ```python
  # Keep Colab from disconnecting
  function ClickConnect(){
    console.log("Working");
    document.querySelector("colab-toolbar-button#connect").click()
  }
  setInterval(ClickConnect, 60000)
  ```

### 8.2 Common Issues on Raspberry Pi

- **Memory Limitations**: If encountering memory issues:
  - Increase swap space
  - Use smaller batch sizes for inference
  - Optimize model for edge devices

- **Camera Issues**: Ensure proper configuration:
  ```bash
  # Enable camera interface
  sudo raspi-config
  # Select: Interface Options > Camera > Enable
  ```

## 9. Performance Optimization

### 9.1 YOLOv8 Optimization for Colab/Raspberry Pi

```python
# Use smaller model variant
model = YOLO('yolov8n.pt')  # nano model

# Export to optimized formats
model.export(format='onnx')  # ONNX format
model.export(format='tflite')  # TensorFlow Lite
```

### 9.2 Dashboard Optimization

For Raspberry Pi, consider:
- Reducing polling frequency
- Limiting displayed data points
- Using server-side rendering for complex visualizations

## 10. Data Management

### 10.1 Efficient Storage in Colab

```python
# Save model checkpoints to Google Drive
!cp -r runs/detect/train /content/drive/MyDrive/solar_models/

# Save processed data
!cp dust_riyadh.json /content/drive/MyDrive/solar_data/
```

### 10.2 Database Options

- SQLite for development/testing
- PostgreSQL for production
- InfluxDB for time-series data (optional)

## 11. Conclusion

This guide ensures that the Solar AI Cleaning & Monitoring System can be run in both Google Colab for development and testing, and on Raspberry Pi for deployment. By following these instructions, you can maintain compatibility across different environments while leveraging the strengths of each platform.

