#!/usr/bin/env python3
"""
Arabic PowerPoint Presentation Generator for Solar AI Cleaning & Monitoring System

This script creates a professional Arabic PowerPoint presentation with:
- RTL text support
- Professional design
- Charts and graphs
- Economic and environmental benefits
- Competitive analysis

Requirements:
- pip install python-pptx matplotlib seaborn arabic-reshaper python-bidi
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import arabic_reshaper
from bidi.algorithm import get_display

# Set up Arabic text handling
def format_arabic_text(text):
    """Format Arabic text for proper display"""
    reshaped_text = arabic_reshaper.reshape(text)
    return get_display(reshaped_text)

# Create presentation
prs = Presentation()

# Define color scheme
BLUE_DARK = RGBColor(44, 62, 80)      # #2c3e50
BLUE_LIGHT = RGBColor(52, 152, 219)   # #3498db
GREEN = RGBColor(39, 174, 96)         # #27ae60
ORANGE = RGBColor(243, 156, 18)       # #f39c12
RED = RGBColor(231, 76, 60)           # #e74c3c
PURPLE = RGBColor(155, 89, 182)       # #9b59b6
WHITE = RGBColor(255, 255, 255)
GRAY_LIGHT = RGBColor(236, 240, 241)  # #ecf0f1

def add_title_slide():
    """Add title slide"""
    slide_layout = prs.slide_layouts[0]  # Title slide layout
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    subtitle = slide.placeholders[1]
    
    # Main title
    title.text = format_arabic_text("نظام مراقبة وتنظيف الألواح الشمسية المدعوم بالذكاء الاصطناعي")
    title.text_frame.paragraphs[0].font.size = Pt(44)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = BLUE_DARK
    
    # Subtitle
    subtitle.text = format_arabic_text("حل متكامل ومبتكر لتحسين كفاءة الطاقة الشمسية في البيئات الصحراوية")
    subtitle.text_frame.paragraphs[0].font.size = Pt(24)
    subtitle.text_frame.paragraphs[0].font.color.rgb = BLUE_LIGHT
    
    return slide

def add_problem_slide():
    """Add problem statement slide"""
    slide_layout = prs.slide_layouts[1]  # Title and content layout
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = format_arabic_text("التحديات الحالية")
    title.text_frame.paragraphs[0].font.size = Pt(36)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = BLUE_DARK
    
    # Add content
    content = slide.placeholders[1]
    tf = content.text_frame
    tf.clear()
    
    problems = [
        "تراكم الغبار يقلل من كفاءة الألواح بنسبة تصل إلى 35%",
        "الصيانة التقليدية تتطلب تدخل بشري مكثف ومكلف",
        "استهلاك كبير للمياه في عمليات التنظيف",
        "صعوبة اكتشاف العيوب والشقوق في الوقت المناسب"
    ]
    
    for i, problem in enumerate(problems):
        p = tf.paragraphs[i] if i == 0 else tf.add_paragraph()
        p.text = format_arabic_text(f"• {problem}")
        p.font.size = Pt(20)
        p.font.color.rgb = BLUE_DARK
        p.space_after = Pt(12)
    
    return slide

def add_solution_slide():
    """Add solution slide"""
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = format_arabic_text("حلنا المبتكر")
    title.text_frame.paragraphs[0].font.size = Pt(36)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = BLUE_DARK
    
    # Add content
    content = slide.placeholders[1]
    tf = content.text_frame
    tf.clear()
    
    solutions = [
        "نموذج YOLOv9: كشف فوري ودقيق للغبار والشقوق والنقاط الساخنة",
        "التصوير الحراري: اكتشاف النقاط الساخنة باستخدام كاميرات FLIR",
        "الطائرات بدون طيار: تنظيف سريع وفعال للمساحات الكبيرة",
        "التحليل التنبؤي: جدولة الصيانة بناءً على البيانات الذكية",
        "لوحة التحكم: مراقبة شاملة في الوقت الفعلي"
    ]
    
    for i, solution in enumerate(solutions):
        p = tf.paragraphs[i] if i == 0 else tf.add_paragraph()
        p.text = format_arabic_text(f"✓ {solution}")
        p.font.size = Pt(18)
        p.font.color.rgb = GREEN
        p.space_after = Pt(10)
    
    return slide

def add_technology_slide():
    """Add technology components slide"""
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = format_arabic_text("المكونات التقنية")
    title.text_frame.paragraphs[0].font.size = Pt(36)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = BLUE_DARK
    
    # Add accuracy chart
    left = Inches(1)
    top = Inches(2)
    width = Inches(8)
    height = Inches(4)
    
    # Create a simple chart placeholder
    chart_shape = slide.shapes.add_textbox(left, top, width, height)
    tf = chart_shape.text_frame
    tf.text = format_arabic_text("دقة الكشف:\n• كشف الغبار: 98%\n• كشف الشقوق: 95%\n• كشف النقاط الساخنة: 97%")
    tf.paragraphs[0].font.size = Pt(24)
    tf.paragraphs[0].font.bold = True
    tf.paragraphs[0].font.color.rgb = BLUE_DARK
    
    return slide

def add_economic_benefits_slide():
    """Add economic benefits slide"""
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = format_arabic_text("الفوائد الاقتصادية")
    title.text_frame.paragraphs[0].font.size = Pt(36)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = BLUE_DARK
    
    # Add economic data
    left = Inches(1)
    top = Inches(2)
    width = Inches(8)
    height = Inches(4)
    
    economic_shape = slide.shapes.add_textbox(left, top, width, height)
    tf = economic_shape.text_frame
    
    economic_data = [
        "التكلفة الأولية: $67,000 (توفير 52% مقارنة بالطرق التقليدية)",
        "التكاليف التشغيلية السنوية: $5,000 (توفير 75%)",
        "استهلاك المياه: 0.1 لتر/م² (توفير 85%)",
        "زيادة الكفاءة: 15-25% (تحسن 56%)",
        "العائد على الاستثمار: $3.7M خلال 5 سنوات"
    ]
    
    for i, data in enumerate(economic_data):
        p = tf.paragraphs[i] if i == 0 else tf.add_paragraph()
        p.text = format_arabic_text(f"💰 {data}")
        p.font.size = Pt(16)
        p.font.color.rgb = GREEN
        p.space_after = Pt(8)
    
    return slide

def add_environmental_benefits_slide():
    """Add environmental benefits slide"""
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = format_arabic_text("الفوائد البيئية")
    title.text_frame.paragraphs[0].font.size = Pt(36)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = BLUE_DARK
    
    # Add environmental data
    left = Inches(1)
    top = Inches(2)
    width = Inches(8)
    height = Inches(4)
    
    env_shape = slide.shapes.add_textbox(left, top, width, height)
    tf = env_shape.text_frame
    
    env_data = [
        "توفير 85% في استهلاك المياه",
        "15,000 MWh طاقة إضافية سنوياً",
        "7,500 طن CO₂ موفرة سنوياً",
        "تقنيات صديقة للبيئة الصحراوية",
        "دعم أهداف رؤية 2030 للاستدامة"
    ]
    
    for i, data in enumerate(env_data):
        p = tf.paragraphs[i] if i == 0 else tf.add_paragraph()
        p.text = format_arabic_text(f"🌱 {data}")
        p.font.size = Pt(18)
        p.font.color.rgb = GREEN
        p.space_after = Pt(10)
    
    return slide

def add_competitive_analysis_slide():
    """Add competitive analysis slide"""
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = format_arabic_text("التحليل التنافسي")
    title.text_frame.paragraphs[0].font.size = Pt(36)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = BLUE_DARK
    
    # Add competitive advantages
    left = Inches(1)
    top = Inches(2)
    width = Inches(8)
    height = Inches(4)
    
    comp_shape = slide.shapes.add_textbox(left, top, width, height)
    tf = comp_shape.text_frame
    
    advantages = [
        "الريادة التقنية: الحل الوحيد مع تكامل شامل للذكاء الاصطناعي",
        "الريادة في التكلفة: أقل تكلفة إجمالية للملكية (52% توفير)",
        "الريادة البيئية: أدنى استهلاك للمياه والطاقة",
        "الريادة الإقليمية: مُحسَّن لظروف الشرق الأوسط",
        "سرعة التنظيف: 1 MW/ساعة مقابل 0.4-0.5 MW/ساعة للمنافسين"
    ]
    
    for i, advantage in enumerate(advantages):
        p = tf.paragraphs[i] if i == 0 else tf.add_paragraph()
        p.text = format_arabic_text(f"🏆 {advantage}")
        p.font.size = Pt(16)
        p.font.color.rgb = BLUE_DARK
        p.space_after = Pt(8)
    
    return slide

def add_implementation_slide():
    """Add implementation plan slide"""
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = format_arabic_text("خطة التنفيذ")
    title.text_frame.paragraphs[0].font.size = Pt(36)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = BLUE_DARK
    
    # Add implementation phases
    left = Inches(1)
    top = Inches(2)
    width = Inches(8)
    height = Inches(4)
    
    impl_shape = slide.shapes.add_textbox(left, top, width, height)
    tf = impl_shape.text_frame
    
    phases = [
        "المرحلة الأولى (4 أسابيع): تصميم النظام وتدريب نماذج الذكاء الاصطناعي",
        "المرحلة الثانية (6 أسابيع): تطوير البرمجيات ولوحة التحكم",
        "المرحلة الثالثة (4 أسابيع): الاختبار الميداني والتحسينات",
        "المرحلة الرابعة (2 أسبوع): النشر النهائي والتدريب",
        "إجمالي المدة: 16 أسبوع للتنفيذ الكامل"
    ]
    
    for i, phase in enumerate(phases):
        p = tf.paragraphs[i] if i == 0 else tf.add_paragraph()
        p.text = format_arabic_text(f"📅 {phase}")
        p.font.size = Pt(16)
        p.font.color.rgb = PURPLE
        p.space_after = Pt(8)
    
    return slide

def add_cta_slide():
    """Add call to action slide"""
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = format_arabic_text("انضم إلى ثورة الطاقة الشمسية الذكية")
    title.text_frame.paragraphs[0].font.size = Pt(32)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = BLUE_DARK
    
    # Add CTA content
    left = Inches(1)
    top = Inches(2.5)
    width = Inches(8)
    height = Inches(3)
    
    cta_shape = slide.shapes.add_textbox(left, top, width, height)
    tf = cta_shape.text_frame
    tf.text = format_arabic_text("كن جزءاً من المستقبل المستدام مع تقنياتنا المبتكرة\n\n📧 <EMAIL>\n📱 +966 50 123 4567\n🌐 www.solar-ai-monitoring.com")
    tf.paragraphs[0].font.size = Pt(20)
    tf.paragraphs[0].font.color.rgb = BLUE_DARK
    tf.paragraphs[0].alignment = PP_ALIGN.CENTER
    
    return slide

def add_thank_you_slide():
    """Add thank you slide"""
    slide_layout = prs.slide_layouts[0]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    subtitle = slide.placeholders[1]
    
    title.text = format_arabic_text("شكراً لكم")
    title.text_frame.paragraphs[0].font.size = Pt(48)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = BLUE_DARK
    
    subtitle.text = format_arabic_text("نحو مستقبل أكثر استدامة\n🚀 الابتكار • 🌱 الاستدامة • 💡 الذكاء الاصطناعي")
    subtitle.text_frame.paragraphs[0].font.size = Pt(24)
    subtitle.text_frame.paragraphs[0].font.color.rgb = BLUE_LIGHT
    
    return slide

def main():
    """Generate the complete presentation"""
    print("Creating Arabic Solar AI Monitoring Presentation...")
    
    # Add all slides
    add_title_slide()
    add_problem_slide()
    add_solution_slide()
    add_technology_slide()
    add_economic_benefits_slide()
    add_environmental_benefits_slide()
    add_competitive_analysis_slide()
    add_implementation_slide()
    add_cta_slide()
    add_thank_you_slide()
    
    # Save presentation
    filename = "نظام_مراقبة_الألواح_الشمسية_بالذكاء_الاصطناعي.pptx"
    prs.save(filename)
    print(f"Presentation saved as: {filename}")
    print("✅ Arabic PowerPoint presentation created successfully!")

if __name__ == "__main__":
    main()
