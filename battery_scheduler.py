#!/usr/bin/env python3
"""
Battery Scheduler for Solar Panel Cleaning System

This script optimizes cleaning schedules based on battery State of Charge (SOC),
weather conditions, and energy production forecasts to ensure cleaning operations
occur when there's sufficient power and minimal impact on energy generation.

Author: Solar AI Cleaning & Monitoring Team
License: MIT
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import requests

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BatteryScheduler:
    def __init__(self, config_file: str = "scheduler_config.json"):
        """
        Initialize the battery scheduler with configuration.
        
        Args:
            config_file: Path to configuration file
        """
        self.config = self.load_config(config_file)
        self.min_soc_threshold = self.config.get("min_soc_threshold", 70)  # Minimum 70% SOC
        self.cleaning_power_consumption = self.config.get("cleaning_power_consumption", 500)  # Watts
        self.cleaning_duration = self.config.get("cleaning_duration", 30)  # Minutes
        self.weather_api_key = self.config.get("weather_api_key", "")
        
    def load_config(self, config_file: str) -> Dict:
        """Load configuration from JSON file."""
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning(f"Config file {config_file} not found. Using defaults.")
            return self.get_default_config()
    
    def get_default_config(self) -> Dict:
        """Return default configuration."""
        return {
            "min_soc_threshold": 70,
            "cleaning_power_consumption": 500,
            "cleaning_duration": 30,
            "preferred_cleaning_hours": [6, 7, 8, 16, 17, 18],  # Early morning and late afternoon
            "weather_api_key": "",
            "panels": [
                {"id": "A23", "priority": 1, "last_cleaned": "2025-06-18"},
                {"id": "B15", "priority": 2, "last_cleaned": "2025-06-10"},
                {"id": "C07", "priority": 3, "last_cleaned": "2025-06-05"}
            ]
        }
    
    def get_current_soc(self, panel_id: str) -> float:
        """
        Get current State of Charge for a specific panel's battery system.
        In a real implementation, this would connect to the battery management system.
        """
        # Mock data for demonstration
        mock_soc_data = {
            "A23": 85.0,
            "B15": 78.0,
            "C07": 65.0
        }
        return mock_soc_data.get(panel_id, 75.0)
    
    def get_weather_forecast(self, lat: float, lon: float) -> Dict:
        """
        Get weather forecast for the next 24 hours.
        """
        if not self.weather_api_key:
            # Return mock data if no API key
            return {
                "forecast": [
                    {"hour": 6, "cloud_cover": 10, "wind_speed": 5},
                    {"hour": 7, "cloud_cover": 15, "wind_speed": 8},
                    {"hour": 8, "cloud_cover": 20, "wind_speed": 12},
                    {"hour": 16, "cloud_cover": 25, "wind_speed": 10},
                    {"hour": 17, "cloud_cover": 30, "wind_speed": 7},
                    {"hour": 18, "cloud_cover": 35, "wind_speed": 5}
                ]
            }
        
        # Real weather API call would go here
        try:
            url = f"http://api.openweathermap.org/data/2.5/forecast"
            params = {
                "lat": lat,
                "lon": lon,
                "appid": self.weather_api_key,
                "units": "metric"
            }
            response = requests.get(url, params=params, timeout=10)
            return response.json()
        except Exception as e:
            logger.error(f"Weather API error: {e}")
            return self.get_weather_forecast(lat, lon)  # Fallback to mock data
    
    def calculate_energy_requirement(self) -> float:
        """
        Calculate total energy requirement for cleaning operation.
        
        Returns:
            Energy requirement in Wh (Watt-hours)
        """
        return (self.cleaning_power_consumption * self.cleaning_duration) / 60
    
    def estimate_solar_generation(self, hour: int, cloud_cover: int) -> float:
        """
        Estimate solar energy generation for a given hour and weather conditions.
        
        Args:
            hour: Hour of the day (0-23)
            cloud_cover: Cloud cover percentage (0-100)
            
        Returns:
            Estimated generation in Watts
        """
        # Peak generation hours (10 AM to 2 PM)
        if 10 <= hour <= 14:
            base_generation = 1000  # Peak generation
        elif 8 <= hour <= 16:
            base_generation = 800   # Good generation
        elif 6 <= hour <= 18:
            base_generation = 400   # Moderate generation
        else:
            base_generation = 0     # No generation
        
        # Adjust for cloud cover
        cloud_factor = (100 - cloud_cover) / 100
        return base_generation * cloud_factor
    
    def find_optimal_cleaning_time(self, panel_id: str, lat: float = 24.7136, lon: float = 46.6753) -> Optional[Dict]:
        """
        Find the optimal time for cleaning based on SOC, weather, and energy generation.
        
        Args:
            panel_id: ID of the panel to schedule cleaning for
            lat: Latitude of the panel location
            lon: Longitude of the panel location
            
        Returns:
            Dictionary with optimal cleaning schedule or None if no suitable time found
        """
        current_soc = self.get_current_soc(panel_id)
        
        if current_soc < self.min_soc_threshold:
            logger.warning(f"Panel {panel_id} SOC ({current_soc}%) below threshold ({self.min_soc_threshold}%)")
            return None
        
        weather_data = self.get_weather_forecast(lat, lon)
        energy_requirement = self.calculate_energy_requirement()
        
        preferred_hours = self.config.get("preferred_cleaning_hours", [6, 7, 8, 16, 17, 18])
        
        best_time = None
        best_score = -1
        
        for hour in preferred_hours:
            # Get weather for this hour (simplified)
            cloud_cover = 20  # Default value
            wind_speed = 5    # Default value
            
            if weather_data and "forecast" in weather_data:
                for forecast in weather_data["forecast"]:
                    if forecast["hour"] == hour:
                        cloud_cover = forecast["cloud_cover"]
                        wind_speed = forecast["wind_speed"]
                        break
            
            # Estimate solar generation
            estimated_generation = self.estimate_solar_generation(hour, cloud_cover)
            
            # Calculate score based on multiple factors
            score = self.calculate_cleaning_score(
                current_soc, estimated_generation, energy_requirement, 
                cloud_cover, wind_speed, hour
            )
            
            if score > best_score:
                best_score = score
                best_time = {
                    "panel_id": panel_id,
                    "scheduled_hour": hour,
                    "current_soc": current_soc,
                    "estimated_generation": estimated_generation,
                    "energy_requirement": energy_requirement,
                    "cloud_cover": cloud_cover,
                    "wind_speed": wind_speed,
                    "score": score,
                    "recommended": True
                }
        
        return best_time
    
    def calculate_cleaning_score(self, soc: float, generation: float, requirement: float, 
                               cloud_cover: int, wind_speed: float, hour: int) -> float:
        """
        Calculate a score for cleaning suitability.
        
        Higher score = better time for cleaning
        """
        score = 0
        
        # SOC factor (higher SOC = better)
        soc_factor = (soc - self.min_soc_threshold) / (100 - self.min_soc_threshold)
        score += soc_factor * 30
        
        # Energy balance factor
        energy_surplus = generation - requirement
        if energy_surplus > 0:
            score += min(energy_surplus / requirement, 1) * 25
        else:
            score -= abs(energy_surplus) / requirement * 20
        
        # Weather factor (low cloud cover and moderate wind = better)
        weather_factor = (100 - cloud_cover) / 100
        score += weather_factor * 20
        
        # Wind factor (moderate wind helps with cleaning)
        if 5 <= wind_speed <= 15:
            score += 10
        elif wind_speed > 20:
            score -= 10
        
        # Time preference (early morning and late afternoon preferred)
        if hour in [6, 7, 8]:
            score += 15  # Early morning preferred
        elif hour in [16, 17, 18]:
            score += 10  # Late afternoon acceptable
        
        return max(0, score)
    
    def schedule_cleaning_for_all_panels(self) -> List[Dict]:
        """
        Schedule cleaning for all panels based on priority and conditions.
        
        Returns:
            List of cleaning schedules
        """
        panels = self.config.get("panels", [])
        schedules = []
        
        # Sort panels by priority and last cleaned date
        panels_sorted = sorted(panels, key=lambda x: (x.get("priority", 999), x.get("last_cleaned", "1900-01-01")))
        
        for panel in panels_sorted:
            panel_id = panel["id"]
            schedule = self.find_optimal_cleaning_time(panel_id)
            
            if schedule:
                schedules.append(schedule)
                logger.info(f"Scheduled cleaning for panel {panel_id} at hour {schedule['scheduled_hour']}")
            else:
                logger.warning(f"Could not schedule cleaning for panel {panel_id}")
        
        return schedules
    
    def save_schedule(self, schedules: List[Dict], filename: str = "cleaning_schedule.json"):
        """Save cleaning schedule to file."""
        schedule_data = {
            "generated_at": datetime.now().isoformat(),
            "schedules": schedules
        }
        
        with open(filename, 'w') as f:
            json.dump(schedule_data, f, indent=2)
        
        logger.info(f"Schedule saved to {filename}")

def main():
    """Main function to run the battery scheduler."""
    scheduler = BatteryScheduler()
    
    logger.info("Starting battery-based cleaning scheduler...")
    
    # Schedule cleaning for all panels
    schedules = scheduler.schedule_cleaning_for_all_panels()
    
    if schedules:
        # Save schedule
        scheduler.save_schedule(schedules)
        
        # Print summary
        print("\n=== Cleaning Schedule Summary ===")
        for schedule in schedules:
            print(f"Panel {schedule['panel_id']}: Hour {schedule['scheduled_hour']}, "
                  f"SOC: {schedule['current_soc']:.1f}%, Score: {schedule['score']:.1f}")
    else:
        print("No cleaning could be scheduled at this time.")

if __name__ == "__main__":
    main()

