#!/usr/bin/env python3
"""
WhatsApp Notification System for Solar Panel Monitoring

This script sends WhatsApp alerts via Twilio when solar panel production drops
below specified thresholds or when maintenance is required.

Author: Solar AI Cleaning & Monitoring Team
License: MIT

Requirements:
- pip install twilio
- Twilio account with WhatsApp sandbox or approved WhatsApp Business API
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from twilio.rest import Client
from twilio.base.exceptions import TwilioException

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WhatsAppNotifier:
    def __init__(self, config_file: str = "notification_config.json"):
        """
        Initialize WhatsApp notifier with Twilio credentials.
        
        Args:
            config_file: Path to configuration file containing Twilio credentials
        """
        self.config = self.load_config(config_file)
        self.account_sid = self.config.get("twilio_account_sid", os.getenv("TWILIO_ACCOUNT_SID"))
        self.auth_token = self.config.get("twilio_auth_token", os.getenv("TWILIO_AUTH_TOKEN"))
        self.from_whatsapp = self.config.get("from_whatsapp_number", "whatsapp:+***********")  # Twilio sandbox
        self.to_whatsapp = self.config.get("to_whatsapp_number", "whatsapp:+**********")
        
        if not self.account_sid or not self.auth_token:
            raise ValueError("Twilio credentials not found. Please set TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN environment variables or provide them in config file.")
        
        self.client = Client(self.account_sid, self.auth_token)
        self.production_threshold = self.config.get("production_threshold", 80)  # 80% of expected production
        self.last_notification_times = {}
        self.notification_cooldown = self.config.get("notification_cooldown_hours", 2)  # Hours between notifications
        
    def load_config(self, config_file: str) -> Dict:
        """Load configuration from JSON file."""
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning(f"Config file {config_file} not found. Using defaults.")
            return self.get_default_config()
    
    def get_default_config(self) -> Dict:
        """Return default configuration."""
        return {
            "twilio_account_sid": "",
            "twilio_auth_token": "",
            "from_whatsapp_number": "whatsapp:+***********",
            "to_whatsapp_number": "whatsapp:+**********",
            "production_threshold": 80,
            "notification_cooldown_hours": 2,
            "language": "en",  # "en" for English, "ar" for Arabic
            "recipients": [
                {"name": "Site Manager", "number": "whatsapp:+**********"},
                {"name": "Maintenance Team", "number": "whatsapp:+**********"}
            ]
        }
    
    def get_panel_production_data(self, panel_id: str) -> Dict:
        """
        Get current production data for a panel.
        In a real implementation, this would connect to the monitoring system.
        """
        # Mock data for demonstration
        mock_data = {
            "A23": {"current_production": 950, "expected_production": 1000, "efficiency": 95},
            "B15": {"current_production": 600, "expected_production": 1000, "efficiency": 60},
            "C07": {"current_production": 500, "expected_production": 1000, "efficiency": 50}
        }
        return mock_data.get(panel_id, {"current_production": 0, "expected_production": 1000, "efficiency": 0})
    
    def should_send_notification(self, panel_id: str, alert_type: str) -> bool:
        """
        Check if enough time has passed since the last notification to avoid spam.
        
        Args:
            panel_id: ID of the panel
            alert_type: Type of alert (production_drop, maintenance_required, etc.)
            
        Returns:
            True if notification should be sent, False otherwise
        """
        key = f"{panel_id}_{alert_type}"
        last_time = self.last_notification_times.get(key)
        
        if not last_time:
            return True
        
        time_diff = datetime.now() - last_time
        return time_diff.total_seconds() > (self.notification_cooldown * 3600)
    
    def format_message(self, alert_type: str, panel_id: str, data: Dict) -> str:
        """
        Format notification message based on alert type and language preference.
        
        Args:
            alert_type: Type of alert
            panel_id: Panel identifier
            data: Additional data for the message
            
        Returns:
            Formatted message string
        """
        language = self.config.get("language", "en")
        
        if language == "ar":
            return self.format_arabic_message(alert_type, panel_id, data)
        else:
            return self.format_english_message(alert_type, panel_id, data)
    
    def format_english_message(self, alert_type: str, panel_id: str, data: Dict) -> str:
        """Format message in English."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        if alert_type == "production_drop":
            return f"""🚨 SOLAR PANEL ALERT 🚨

Panel ID: {panel_id}
Issue: Production Drop Detected
Current Production: {data.get('current_production', 0)}W
Expected Production: {data.get('expected_production', 0)}W
Efficiency: {data.get('efficiency', 0)}%

Time: {timestamp}

Action Required: Please check panel for dust, damage, or shading issues.

Solar AI Monitoring System"""
        
        elif alert_type == "maintenance_required":
            return f"""🔧 MAINTENANCE ALERT 🔧

Panel ID: {panel_id}
Issue: Maintenance Required
Defects Detected: {', '.join(data.get('defects', []))}
Last Cleaned: {data.get('last_cleaned', 'Unknown')}

Time: {timestamp}

Action Required: Schedule cleaning or inspection.

Solar AI Monitoring System"""
        
        elif alert_type == "cleaning_completed":
            return f"""✅ CLEANING COMPLETED ✅

Panel ID: {panel_id}
Status: Cleaning Successfully Completed
New Efficiency: {data.get('efficiency', 0)}%
Improvement: +{data.get('improvement', 0)}%

Time: {timestamp}

Solar AI Monitoring System"""
        
        else:
            return f"""📊 SOLAR PANEL NOTIFICATION 📊

Panel ID: {panel_id}
Alert Type: {alert_type}
Time: {timestamp}

Solar AI Monitoring System"""
    
    def format_arabic_message(self, alert_type: str, panel_id: str, data: Dict) -> str:
        """Format message in Arabic."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        if alert_type == "production_drop":
            return f"""🚨 تنبيه الألواح الشمسية 🚨

معرف اللوح: {panel_id}
المشكلة: انخفاض في الإنتاج
الإنتاج الحالي: {data.get('current_production', 0)} واط
الإنتاج المتوقع: {data.get('expected_production', 0)} واط
الكفاءة: {data.get('efficiency', 0)}%

الوقت: {timestamp}

الإجراء المطلوب: يرجى فحص اللوح للتأكد من عدم وجود غبار أو تلف أو ظلال.

نظام مراقبة الذكاء الاصطناعي الشمسي"""
        
        elif alert_type == "maintenance_required":
            return f"""🔧 تنبيه الصيانة 🔧

معرف اللوح: {panel_id}
المشكلة: صيانة مطلوبة
العيوب المكتشفة: {', '.join(data.get('defects', []))}
آخر تنظيف: {data.get('last_cleaned', 'غير معروف')}

الوقت: {timestamp}

الإجراء المطلوب: جدولة التنظيف أو الفحص.

نظام مراقبة الذكاء الاصطناعي الشمسي"""
        
        elif alert_type == "cleaning_completed":
            return f"""✅ اكتمل التنظيف ✅

معرف اللوح: {panel_id}
الحالة: تم التنظيف بنجاح
الكفاءة الجديدة: {data.get('efficiency', 0)}%
التحسن: +{data.get('improvement', 0)}%

الوقت: {timestamp}

نظام مراقبة الذكاء الاصطناعي الشمسي"""
        
        else:
            return f"""📊 إشعار الألواح الشمسية 📊

معرف اللوح: {panel_id}
نوع التنبيه: {alert_type}
الوقت: {timestamp}

نظام مراقبة الذكاء الاصطناعي الشمسي"""
    
    def send_whatsapp_message(self, message: str, to_number: Optional[str] = None) -> bool:
        """
        Send WhatsApp message via Twilio.
        
        Args:
            message: Message content
            to_number: Recipient WhatsApp number (optional, uses default if not provided)
            
        Returns:
            True if message sent successfully, False otherwise
        """
        try:
            recipient = to_number or self.to_whatsapp
            
            message_instance = self.client.messages.create(
                body=message,
                from_=self.from_whatsapp,
                to=recipient
            )
            
            logger.info(f"WhatsApp message sent successfully. SID: {message_instance.sid}")
            return True
            
        except TwilioException as e:
            logger.error(f"Twilio error: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error sending WhatsApp message: {e}")
            return False
    
    def check_and_notify_production_drop(self, panel_id: str) -> bool:
        """
        Check panel production and send notification if below threshold.
        
        Args:
            panel_id: ID of the panel to check
            
        Returns:
            True if notification was sent, False otherwise
        """
        if not self.should_send_notification(panel_id, "production_drop"):
            logger.info(f"Skipping notification for {panel_id} - cooldown period active")
            return False
        
        production_data = self.get_panel_production_data(panel_id)
        current_production = production_data["current_production"]
        expected_production = production_data["expected_production"]
        
        if expected_production > 0:
            efficiency_percentage = (current_production / expected_production) * 100
        else:
            efficiency_percentage = 0
        
        if efficiency_percentage < self.production_threshold:
            message = self.format_message("production_drop", panel_id, {
                "current_production": current_production,
                "expected_production": expected_production,
                "efficiency": round(efficiency_percentage, 1)
            })
            
            success = self.send_whatsapp_message(message)
            
            if success:
                self.last_notification_times[f"{panel_id}_production_drop"] = datetime.now()
                logger.info(f"Production drop notification sent for panel {panel_id}")
            
            return success
        
        return False
    
    def notify_maintenance_required(self, panel_id: str, defects: List[str], last_cleaned: str) -> bool:
        """
        Send maintenance required notification.
        
        Args:
            panel_id: ID of the panel
            defects: List of detected defects
            last_cleaned: Date of last cleaning
            
        Returns:
            True if notification was sent, False otherwise
        """
        if not self.should_send_notification(panel_id, "maintenance_required"):
            return False
        
        message = self.format_message("maintenance_required", panel_id, {
            "defects": defects,
            "last_cleaned": last_cleaned
        })
        
        success = self.send_whatsapp_message(message)
        
        if success:
            self.last_notification_times[f"{panel_id}_maintenance_required"] = datetime.now()
            logger.info(f"Maintenance notification sent for panel {panel_id}")
        
        return success
    
    def notify_cleaning_completed(self, panel_id: str, new_efficiency: float, improvement: float) -> bool:
        """
        Send cleaning completed notification.
        
        Args:
            panel_id: ID of the panel
            new_efficiency: New efficiency after cleaning
            improvement: Efficiency improvement percentage
            
        Returns:
            True if notification was sent, False otherwise
        """
        message = self.format_message("cleaning_completed", panel_id, {
            "efficiency": round(new_efficiency, 1),
            "improvement": round(improvement, 1)
        })
        
        success = self.send_whatsapp_message(message)
        
        if success:
            logger.info(f"Cleaning completion notification sent for panel {panel_id}")
        
        return success
    
    def send_to_all_recipients(self, message: str) -> int:
        """
        Send message to all configured recipients.
        
        Args:
            message: Message to send
            
        Returns:
            Number of successful sends
        """
        recipients = self.config.get("recipients", [{"number": self.to_whatsapp}])
        success_count = 0
        
        for recipient in recipients:
            number = recipient.get("number")
            if number and self.send_whatsapp_message(message, number):
                success_count += 1
        
        return success_count

def main():
    """Main function for testing the notification system."""
    try:
        notifier = WhatsAppNotifier()
        
        # Test production drop notification
        print("Testing production drop notification...")
        notifier.check_and_notify_production_drop("B15")
        
        # Test maintenance notification
        print("Testing maintenance notification...")
        notifier.notify_maintenance_required("C07", ["dust", "crack"], "2025-06-05")
        
        # Test cleaning completion notification
        print("Testing cleaning completion notification...")
        notifier.notify_cleaning_completed("A23", 95.0, 15.0)
        
    except Exception as e:
        logger.error(f"Error in main: {e}")

if __name__ == "__main__":
    main()

